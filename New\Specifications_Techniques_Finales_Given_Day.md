# Spécifications Techniques Finales - Given Day MVP

## 📋 Vue d'ensemble du projet

**Projet :** Given Day - Réseau social professionnel chrétien  
**Client :** Association chrétienne (modèle hybride association + entreprise sociale)  
**Équipe de développement :** Madagascar (approche solidaire)  
**Architecte Technique & Lead Mobile :** [Votre nom]  
**Date de lancement cible :** Janvier 2026  

## 🎯 Objectifs quantitatifs confirmés

- **3 premiers mois :** 300 utilisateurs
- **Seuil V2 :** 1 000 utilisateurs  
- **1 an :** 900+ utilisateurs actifs
- **3 ans :** 15 000+ utilisateurs
- **Cible géographique :** France, Belgique, Luxembourg (francophone)

## 🆕 Fonctionnalités spécifiques Given Day (Nouvelles)

### 1. Encouragement du Jour Donné
**Priorité : HAUTE**

- **Description :** Verset/texte inspirant quotidien affiché à l'accueil
- **Fonctionnalités techniques :**
  - Système de planification de contenu
  - Interface d'administration pour modérateurs
  - Système de validation par équipe de bénévoles
  - Historique des encouragements précédents
  - Statistiques d'engagement

**Implémentation technique :**
```javascript
// Structure de données
const DailyEncouragement = {
  id: String,
  date: Date,
  title: String,
  content: String,
  verse_reference: String, // optionnel
  author: String, // bénévole contributeur
  status: Enum['draft', 'pending', 'approved', 'published'],
  moderator_id: String,
  created_at: Date,
  published_at: Date,
  engagement_stats: {
    views: Number,
    reactions: Object,
    shares: Number
  }
}
```

### 2. Boutons d'action pédagogiques
**Priorité : HAUTE**

Trois boutons principaux à l'accueil :

1. **"Témoigner de la bonté de Dieu"**
2. **"Demander une prière"**  
3. **"Partager une révélation"**

**Fonctionnalités :**
- Possibilité de texte libre pour chaque action
- Interface simplifiée et intuitive
- Catégorisation automatique des publications
- Modération spécifique par type de contenu

**Implémentation technique :**
```javascript
const ActionButton = {
  type: Enum['testimony', 'prayer_request', 'revelation'],
  title: String,
  content: String, // texte libre
  user_id: String,
  is_anonymous: Boolean, // option pour demandes de prière
  status: Enum['pending', 'approved', 'published'],
  created_at: Date
}
```

### 3. Réactions personnalisées
**Priorité : HAUTE**

Remplacement des "like" classiques par :
- **"Amen"** - Approbation spirituelle
- **"Alléluia"** - Louange  
- **"God is with you"** - Soutien
- **"Preach it !!"** - Encouragement

**Implémentation technique :**
```javascript
const CustomReaction = {
  type: Enum['amen', 'alleluia', 'god_is_with_you', 'preach_it'],
  user_id: String,
  post_id: String,
  created_at: Date
}
```

## 👥 Système de modération et gestion

### Équipe de modération
- **Modérateur principal :** Salarié responsable de la supervision
- **Animateur :** Responsable de l'Encouragement du Jour Donné
- **Équipe de bénévoles :** Contributeurs de contenu spirituel

### Interface d'administration
**Fonctionnalités requises :**
- Dashboard de modération en temps réel
- Système de validation des contenus
- Gestion des contributeurs bénévoles
- Statistiques d'engagement
- Outils de planification de contenu

## 🏗️ Architecture technique confirmée

### Stack technologique
```yaml
Frontend Mobile:
  - React Native (iOS/Android)
  - Redux pour la gestion d'état
  - React Navigation
  - OneSignal pour notifications

Backend:
  - Node.js + Express.js
  - PostgreSQL (base de données principale)
  - Redis (cache et sessions)
  - Socket.io (temps réel)

Infrastructure:
  - Hostinger VPS (début)
  - Migration vers DigitalOcean selon croissance
  - HTTPS/SSL obligatoire
  - Backup automatisé quotidien

Authentification:
  - JWT + OAuth2
  - Intégration Google/Facebook
  - Système de récupération de mot de passe
```

### Base de données - Schéma principal

```sql
-- Utilisateurs
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  profile_picture_url VARCHAR(500),
  denomination VARCHAR(100), -- optionnel
  professional_title VARCHAR(200),
  church VARCHAR(200),
  location VARCHAR(200),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Encouragements quotidiens
CREATE TABLE daily_encouragements (
  id UUID PRIMARY KEY,
  date DATE UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  verse_reference VARCHAR(100),
  author_id UUID REFERENCES users(id),
  moderator_id UUID REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT NOW(),
  published_at TIMESTAMP
);

-- Publications avec boutons d'action
CREATE TABLE posts (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  content TEXT NOT NULL,
  post_type VARCHAR(20), -- 'testimony', 'prayer_request', 'revelation', 'general'
  is_anonymous BOOLEAN DEFAULT false,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Réactions personnalisées
CREATE TABLE custom_reactions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  post_id UUID REFERENCES posts(id),
  reaction_type VARCHAR(20), -- 'amen', 'alleluia', 'god_is_with_you', 'preach_it'
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, post_id, reaction_type)
);
```

## 🔐 Sécurité et conformité

### Mesures de sécurité
- Chiffrement des données sensibles (bcrypt pour mots de passe)
- Protection CSRF et XSS
- Rate limiting sur les API
- Validation stricte des entrées utilisateur
- Logs de sécurité pour actions sensibles

### Conformité RGPD
- Consentement explicite pour collecte de données
- Droit à l'oubli (suppression de compte)
- Export des données personnelles
- Politique de confidentialité claire
- Minimisation des données collectées

## 📱 Spécifications UX/UI

### Écran d'accueil
1. **Header :** Logo Given Day + notifications
2. **Encouragement du Jour :** Zone mise en valeur
3. **Boutons d'action :** 3 boutons pédagogiques bien visibles
4. **Feed :** Publications de la communauté
5. **Navigation :** Accès groupes, profil, recherche

### Design guidelines
- Couleurs douces et apaisantes (bleus, blancs)
- Symboles chrétiens discrets
- Interface claire et accessible
- Respect des guidelines iOS/Android
- Optimisation pour seniors (taille de police adaptable)

## 🚀 Planning de développement détaillé

### Phase 2 - Design (5 semaines)
- Semaine 1-2 : Wireframes avec nouvelles fonctionnalités
- Semaine 3-4 : Maquettes haute fidélité
- Semaine 5 : Prototypes interactifs et validation

### Phase 3 - Backend (8 semaines)
- Semaine 1-2 : Architecture et base de données
- Semaine 3-4 : API authentification et utilisateurs
- Semaine 5-6 : API encouragements et modération
- Semaine 7-8 : API posts et réactions personnalisées

### Phase 4 - Mobile (12 semaines)
- Semaine 1-3 : Structure app et navigation
- Semaine 4-6 : Écrans principaux et authentification
- Semaine 7-9 : Fonctionnalités spécifiques Given Day
- Semaine 10-12 : Intégration complète et optimisations

## 💰 Budget technique confirmé

**Développement :** 18 000€ HT (TJM 100€ x 180 jours)
**Infrastructure annuelle :** ~300€
- Hostinger VPS : 180€/an
- Domaine : 12€/an
- Apple Developer : 99€/an
- Google Play : 25€ (unique)

## 🔄 Intégrations futures (V2+)

### Hello Bible
- API d'accès aux ressources bibliques
- Synchronisation des versets favoris
- Plans de lecture partagés

### YouVersion  
- Intégration plans de lecture
- Partage de versets annotés
- Synchronisation des progrès

## 📊 Métriques de succès

### Techniques
- Temps de réponse API < 200ms
- Disponibilité > 99.5%
- Temps de chargement app < 3s

### Fonctionnelles
- Taux d'engagement Encouragement du Jour > 60%
- Utilisation boutons d'action > 40% des utilisateurs actifs
- Réactions personnalisées > 80% vs likes classiques

## ✅ Critères de validation MVP

1. **300 utilisateurs** atteints en 3 mois
2. **Système de modération** opérationnel
3. **Encouragement quotidien** publié sans interruption
4. **Boutons d'action** utilisés régulièrement
5. **Réactions personnalisées** adoptées par la communauté
6. **Performance technique** conforme aux standards
7. **Feedback positif** de la communauté chrétienne

---

**Document préparé par :** Architecte Technique & Lead Mobile  
**Date :** Décembre 2024  
**Version :** 1.0 - Spécifications finales post-retour client
